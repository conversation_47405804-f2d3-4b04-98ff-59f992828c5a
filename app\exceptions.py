"""
异常处理模块
"""
import logging
from datetime import datetime
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from .models import ErrorResponse

logger = logging.getLogger(__name__)


class ChatServiceException(Exception):
    """聊天服务异常"""
    def __init__(self, message: str, detail: str = None):
        self.message = message
        self.detail = detail
        super().__init__(self.message)


class DatabaseException(Exception):
    """数据库异常"""
    def __init__(self, message: str, detail: str = None):
        self.message = message
        self.detail = detail
        super().__init__(self.message)


class AIServiceException(Exception):
    """AI服务异常"""
    def __init__(self, message: str, detail: str = None):
        self.message = message
        self.detail = detail
        super().__init__(self.message)


async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.detail,
            timestamp=datetime.now()
        ).dict()
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    logger.error(f"请求验证失败: {exc.errors()}")
    
    return JSONResponse(
        status_code=422,
        content=ErrorResponse(
            error="请求参数验证失败",
            detail=str(exc.errors()),
            timestamp=datetime.now()
        ).dict()
    )


async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {type(exc).__name__} - {str(exc)}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="内部服务器错误",
            detail="服务器遇到了一个意外的错误",
            timestamp=datetime.now()
        ).dict()
    )


async def chat_service_exception_handler(request: Request, exc: ChatServiceException):
    """聊天服务异常处理器"""
    logger.error(f"聊天服务异常: {exc.message} - {exc.detail}")
    
    return JSONResponse(
        status_code=400,
        content=ErrorResponse(
            error=exc.message,
            detail=exc.detail,
            timestamp=datetime.now()
        ).dict()
    )


async def database_exception_handler(request: Request, exc: DatabaseException):
    """数据库异常处理器"""
    logger.error(f"数据库异常: {exc.message} - {exc.detail}")
    
    return JSONResponse(
        status_code=503,
        content=ErrorResponse(
            error="数据库服务不可用",
            detail=exc.detail,
            timestamp=datetime.now()
        ).dict()
    )


async def ai_service_exception_handler(request: Request, exc: AIServiceException):
    """AI服务异常处理器"""
    logger.error(f"AI服务异常: {exc.message} - {exc.detail}")
    
    return JSONResponse(
        status_code=502,
        content=ErrorResponse(
            error="AI服务不可用",
            detail=exc.detail,
            timestamp=datetime.now()
        ).dict()
    )


def setup_exception_handlers(app):
    """设置异常处理器"""
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(ChatServiceException, chat_service_exception_handler)
    app.add_exception_handler(DatabaseException, database_exception_handler)
    app.add_exception_handler(AIServiceException, ai_service_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
