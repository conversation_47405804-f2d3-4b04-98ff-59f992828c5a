"""
数据模型定义
"""
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum


class MessageType(str, Enum):
    """消息类型枚举"""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"


class Role(str, Enum):
    """角色枚举"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class ChatSendRequest(BaseModel):
    """发送消息请求模型"""
    user_id: str = Field(..., description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID，为空时自动生成")
    msg_type: MessageType = Field(MessageType.TEXT, description="消息类型")
    message: Optional[str] = Field(None, description="文本消息内容")
    file_url: Optional[str] = Field(None, description="文件/图片URL")
    
    @validator("message")
    def validate_message(cls, v, values):
        msg_type = values.get("msg_type")
        if msg_type == MessageType.TEXT and not v:
            raise ValueError("文本消息不能为空")
        return v
    
    @validator("file_url")
    def validate_file_url(cls, v, values):
        msg_type = values.get("msg_type")
        if msg_type in [MessageType.IMAGE, MessageType.FILE] and not v:
            raise ValueError("图片/文件消息必须提供file_url")
        return v


class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: Role
    msg_type: MessageType
    content: str
    file_url: Optional[str] = None
    timestamp: datetime


class ChatSendResponse(BaseModel):
    """发送消息响应模型"""
    reply: str = Field(..., description="AI回复内容")
    reply_type: MessageType = Field(MessageType.TEXT, description="回复类型")
    session_id: str = Field(..., description="会话ID")
    echo_fields: Dict[str, Any] = Field(default_factory=dict, description="透传字段")


class ChatHistoryRequest(BaseModel):
    """获取历史消息请求模型"""
    user_id: str
    session_id: str
    limit: int = Field(20, ge=1, le=100, description="消息数量限制")


class ChatHistoryResponse(BaseModel):
    """获取历史消息响应模型"""
    history: List[ChatMessage]


class SessionsResponse(BaseModel):
    """获取会话列表响应模型"""
    sessions: List[str]


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    timestamp: datetime
    version: str


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str
    detail: Optional[str] = None
    timestamp: datetime


# GPT消息格式
class GPTTextContent(BaseModel):
    """GPT文本内容"""
    type: str = "text"
    text: str


class GPTImageContent(BaseModel):
    """GPT图片内容"""
    type: str = "image_url"
    image_url: Dict[str, str]


class GPTMessage(BaseModel):
    """GPT消息格式"""
    role: str
    content: Union[str, List[Union[GPTTextContent, GPTImageContent]]]
