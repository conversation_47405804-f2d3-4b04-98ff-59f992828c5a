"""
聊天相关API路由
"""
import logging
from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.responses import JSONResponse

from ..models import (
    ChatSendRequest, ChatSendResponse,
    ChatHistoryRequest, ChatHistoryResponse,
    SessionsResponse, ErrorResponse
)
from ..services.chat_service import chat_service
from ..security import rate_limit_check, validate_input_length, sanitize_input

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/chat", tags=["聊天"])


@router.post("/send", response_model=ChatSendResponse, dependencies=[Depends(rate_limit_check)])
async def send_message(request: Request):
    """发送消息"""
    try:
        # 解析请求数据
        data = await request.json()
        
        # 分离核心字段和透传字段
        core_fields = {"user_id", "session_id", "msg_type", "message", "file_url"}
        echo_fields = {k: v for k, v in data.items() if k not in core_fields}
        
        # 验证请求数据
        chat_request = ChatSendRequest(**{k: v for k, v in data.items() if k in core_fields})

        # 输入验证和清理
        if chat_request.message:
            chat_request.message = sanitize_input(validate_input_length(chat_request.message))
        
        # 处理消息
        response = await chat_service.send_message(chat_request, echo_fields)
        
        return response
        
    except ValueError as e:
        logger.error(f"请求参数验证失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"发送消息失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/history", response_model=ChatHistoryResponse, dependencies=[Depends(rate_limit_check)])
async def get_chat_history(user_id: str, session_id: str, limit: int = 20):
    """获取聊天历史"""
    try:
        # 验证参数
        if limit < 1 or limit > 100:
            raise HTTPException(status_code=400, detail="limit参数必须在1-100之间")
        
        # 获取历史消息
        history = chat_service.get_chat_history(user_id, session_id, limit)
        
        return ChatHistoryResponse(history=history)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取聊天历史失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/sessions", response_model=SessionsResponse, dependencies=[Depends(rate_limit_check)])
async def get_user_sessions(user_id: str):
    """获取用户会话列表"""
    try:
        sessions = chat_service.get_user_sessions(user_id)
        return SessionsResponse(sessions=sessions)
        
    except Exception as e:
        logger.error(f"获取用户会话失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")
