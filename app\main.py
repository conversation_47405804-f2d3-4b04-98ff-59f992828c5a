"""
主应用模块
"""
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI

from .config import settings
from .database import db_manager
from .middleware import setup_middleware
from .exceptions import setup_exception_handlers
from .logging_config import setup_logging
from .api import chat, health

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("应用启动中...")
    
    try:
        # 连接数据库
        db_manager.connect()
        logger.info("数据库连接成功")
        
        yield
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise
    finally:
        # 关闭时执行
        logger.info("应用关闭中...")
        db_manager.disconnect()
        logger.info("应用已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    
    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.APP_VERSION,
        description="智能客服系统API",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        lifespan=lifespan
    )
    
    # 设置中间件
    setup_middleware(app)
    
    # 设置异常处理器
    setup_exception_handlers(app)
    
    # 注册路由
    app.include_router(chat.router, prefix=settings.API_PREFIX)
    app.include_router(health.router, prefix=settings.API_PREFIX)
    
    return app


# 创建应用实例
app = create_app()


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用{settings.APP_NAME}",
        "version": settings.APP_VERSION,
        "docs_url": "/docs" if settings.DEBUG else "文档已禁用"
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
