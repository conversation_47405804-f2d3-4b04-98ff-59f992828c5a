import uuid
import base64
import requests
from fastapi import Fast<PERSON><PERSON>, Request, UploadFile, File, Form
from fastapi.responses import JSONResponse
from pymongo import MongoClient
from langchain.memory import MongoDBChatMessageHistory
from datetime import datetime
from gpt4o_utils import call_gpt4o, url_to_base64

app = FastAPI()

# ====== 1. MongoDB连接 ======
MONGO_URI = "mongodb://localhost:27017"
mongo_client = MongoClient(MONGO_URI)
DB_NAME = "chat_db"
COLL_NAME = "chat_histories"

def get_history(user_id, session_id):
    return MongoDBChatMessageHistory(
        connection_string=MONGO_URI,
        session_id=f"{user_id}-{session_id}",
        database_name=DB_NAME,
        collection_name=COLL_NAME
    )

# ====== 2. 工具方法 ======
def url_to_base64(url):
    resp = requests.get(url, timeout=5)
    resp.raise_for_status()
    return base64.b64encode(resp.content).decode()

# ====== 3. 发送消息API ======
@app.post("/chat/send")
async def chat_send(request: Request):
    data = await request.json()
    # --- 必要字段 ---
    user_id = data.get("user_id")
    session_id = data.get("session_id") or str(uuid.uuid4())
    msg_type = data.get("msg_type", "text")
    message = data.get("message")
    file_url = data.get("file_url")
    
    # --- 拆分无用字段 ---
    core_fields = {"user_id", "session_id", "msg_type", "message", "file_url"}
    echo_fields = {k: v for k, v in data.items() if k not in core_fields}

    # --- 会话历史对象 ---
    history = get_history(user_id, session_id)
    now_time = datetime.now().isoformat()

    # --- 处理消息入库 ---
    msg_doc = {
        "user_id": user_id,
        "session_id": session_id,
        "role": "user",
        "msg_type": msg_type,
        "content": message if msg_type == "text" else "",
        "file_url": file_url if msg_type in ["image", "file"] else "",
        "timestamp": now_time
    }
    # 手动插入MongoDB原始数据（方便扩展）
    mongo_client[DB_NAME][COLL_NAME].insert_one(msg_doc)
    # 也插入LangChain消息
    if msg_type == "text":
        history.add_user_message(message)
    elif msg_type in ["image", "file"]:
        # 记录图片/文件URL，内容为空或附说明
        history.add_user_message(f"[{msg_type}]({file_url})")

    # --- AI回复部分（此处为占位符，真实项目替换为GPT-4o多模态调用） ---
    chat_history = []
    # 取出最近10轮（可配置）
    for doc in mongo_client[DB_NAME][COLL_NAME].find(
        {"user_id": user_id, "session_id": session_id}
    ).sort("timestamp", 1).limit(10):
        if doc.get("msg_type") == "text":
            chat_history.append({"role": doc.get("role"), "content": doc.get("content")})
        elif doc.get("msg_type") == "image":
            try:
                img_base64 = url_to_base64(doc.get("file_url"))
                chat_history.append({
                    "role": doc.get("role"),
                    "content": [
                        {"type": "text", "text": "图片如下"},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{img_base64}"}}
                    ]
                })
            except Exception as e:
                chat_history.append({
                    "role": doc.get("role"),
                    "content": f"[图片下载失败: {str(e)}]"
                })
        elif doc.get("msg_type") == "file":
            chat_history.append({
                "role": doc.get("role"),
                "content": f"用户上传了一个文件: {doc.get('file_url')}"
            })

    # 新一轮消息拼接
    if msg_type == "text":
        chat_history.append({"role": "user", "content": message})
    elif msg_type == "image":
        img_base64 = url_to_base64(file_url)
        chat_history.append({
            "role": "user",
            "content": [
                {"type": "text", "text": "请识别这张图片"},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{img_base64}"}}
            ]
        })
    elif msg_type == "file":
        chat_history.append({
            "role": "user",
            "content": f"请分析这个文件: {file_url}"
        })

    # 调用GPT-4o
    try:
        reply = call_gpt4o(chat_history)
    except Exception as e:
        reply = f"AI接口调用失败: {str(e)}"

    # --- 记录AI回复 ---
    ai_msg_doc = {
        "user_id": user_id,
        "session_id": session_id,
        "role": "assistant",
        "msg_type": "text",
        "content": reply,
        "file_url": "",
        "timestamp": datetime.now().isoformat()
    }
    mongo_client[DB_NAME][COLL_NAME].insert_one(ai_msg_doc)
    history.add_ai_message(reply)

    # --- 返回结果 ---
    return JSONResponse({
        "reply": reply,
        "reply_type": "text",
        "session_id": session_id,
        "echo_fields": echo_fields  # 透传调用方无用字段
    })

# ====== 4. 获取会话历史API ======
@app.get("/chat/history")
async def get_history_api(user_id: str, session_id: str, limit: int = 20):
    cursor = mongo_client[DB_NAME][COLL_NAME].find({
        "user_id": user_id, "session_id": session_id
    }).sort("timestamp", -1).limit(limit)
    msgs = []
    for doc in cursor:
        msg = {
            "role": doc.get("role"),
            "msg_type": doc.get("msg_type"),
            "content": doc.get("content"),
            "file_url": doc.get("file_url"),
            "timestamp": doc.get("timestamp")
        }
        msgs.append(msg)
    return {"history": list(reversed(msgs))}

# ====== 5. 会话列表API ======
@app.get("/chat/sessions")
async def get_sessions_api(user_id: str):
    cursor = mongo_client[DB_NAME][COLL_NAME].find({"user_id": user_id})
    sessions = set()
    for doc in cursor:
        sessions.add(doc.get("session_id"))
    return {"sessions": list(sessions)}

# ====== 6. 健康检查API ======
@app.get("/health")
async def health_check():
    return {"status": "ok"}

# ====== 7. 运行说明 ======
# 运行方式： uvicorn main:app --reload
