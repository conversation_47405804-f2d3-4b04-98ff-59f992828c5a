"""
健康检查API路由
"""
import logging
from datetime import datetime
from fastapi import APIRouter, HTTPException

from ..models import HealthResponse
from ..config import settings
from ..database import db_manager

logger = logging.getLogger(__name__)
router = APIRouter(tags=["健康检查"])


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        db_status = db_manager.is_connected
        
        if not db_status:
            raise HTTPException(status_code=503, detail="数据库连接失败")
        
        return HealthResponse(
            status="ok",
            timestamp=datetime.now(),
            version=settings.APP_VERSION
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="健康检查失败")


@router.get("/health/detailed")
async def detailed_health_check():
    """详细健康检查"""
    try:
        health_info = {
            "status": "ok",
            "timestamp": datetime.now().isoformat(),
            "version": settings.APP_VERSION,
            "services": {
                "database": {
                    "status": "ok" if db_manager.is_connected else "error",
                    "uri": settings.MONGO_URI.split('@')[-1] if '@' in settings.MONGO_URI else settings.MONGO_URI
                },
                "ai_service": {
                    "status": "ok",
                    "model": settings.OPENAI_MODEL
                }
            },
            "config": {
                "debug": settings.DEBUG,
                "max_history_limit": settings.MAX_HISTORY_LIMIT,
                "max_file_size": settings.MAX_FILE_SIZE
            }
        }
        
        # 检查整体状态
        overall_status = all(
            service["status"] == "ok" 
            for service in health_info["services"].values()
        )
        
        if not overall_status:
            health_info["status"] = "degraded"
        
        return health_info
        
    except Exception as e:
        logger.error(f"详细健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="健康检查失败")
