#!/usr/bin/env python3
"""
配置验证脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from app.config import settings
    print("✅ 配置验证成功")
    print(f"应用名称: {settings.APP_NAME}")
    print(f"版本: {settings.APP_VERSION}")
    print(f"调试模式: {settings.DEBUG}")
    print(f"API端口: {settings.API_PORT}")
    print(f"MongoDB URI: {settings.MONGO_URI}")
    print(f"OpenAI模型: {settings.OPENAI_MODEL}")
    print(f"日志级别: {settings.LOG_LEVEL}")
    
except Exception as e:
    print(f"❌ 配置验证失败: {e}")
    sys.exit(1)
