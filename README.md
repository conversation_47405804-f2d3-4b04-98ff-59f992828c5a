# 智能客服系统

一个基于FastAPI的现代化智能客服系统，支持文本、图片和文件处理，集成GPT-4o多模态AI能力。

## ✨ 特性

- 🚀 **现代化架构**: 基于FastAPI构建，支持异步处理
- 🤖 **AI集成**: 集成OpenAI GPT-4o，支持多模态对话
- 💾 **数据持久化**: 使用MongoDB存储聊天历史
- 🔒 **安全可靠**: 完善的错误处理、日志记录和安全中间件
- 📊 **监控友好**: 内置健康检查和详细日志
- 🐳 **容器化**: 支持Docker部署
- 🧪 **测试覆盖**: 完整的单元测试和集成测试

## 🏗️ 项目结构

```
smart_customer_service/
├── app/                    # 应用主目录
│   ├── __init__.py
│   ├── main.py            # 应用入口
│   ├── config.py          # 配置管理
│   ├── models.py          # 数据模型
│   ├── database.py        # 数据库操作
│   ├── middleware.py      # 中间件
│   ├── exceptions.py      # 异常处理
│   ├── logging_config.py  # 日志配置
│   ├── api/               # API路由
│   │   ├── __init__.py
│   │   ├── chat.py        # 聊天API
│   │   └── health.py      # 健康检查API
│   └── services/          # 业务服务
│       ├── __init__.py
│       ├── ai_service.py  # AI服务
│       └── chat_service.py # 聊天服务
├── tests/                 # 测试文件
├── logs/                  # 日志文件
├── requirements.txt       # Python依赖
├── .env.example          # 环境变量示例
├── Dockerfile            # Docker配置
├── docker-compose.yml    # Docker Compose配置
└── README.md             # 项目文档
```

## 🚀 快速开始

### 方式一：本地开发

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd smart_customer_service
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，设置你的配置
   ```

4. **启动MongoDB**
   ```bash
   # 使用Docker启动MongoDB
   docker run -d --name mongodb -p 27017:27017 mongo:7.0
   ```

5. **启动应用**
   ```bash
   python -m app.main
   # 或者使用uvicorn
   uvicorn app.main:app --reload
   ```

### 方式二：Docker部署

1. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件
   ```

2. **启动服务**
   ```bash
   docker-compose up -d
   ```

3. **查看服务状态**
   ```bash
   docker-compose ps
   ```

## 📚 API文档

启动服务后，访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### 主要API端点

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/v1/chat/send` | 发送消息 |
| GET | `/api/v1/chat/history` | 获取聊天历史 |
| GET | `/api/v1/chat/sessions` | 获取用户会话列表 |
| GET | `/api/v1/health` | 健康检查 |
| GET | `/api/v1/health/detailed` | 详细健康检查 |

### 消息类型

- `text`: 文本消息
- `image`: 图片消息（需要提供file_url）
- `file`: 文件消息（需要提供file_url）

## 🔧 配置说明

主要配置项（在.env文件中设置）：

```env
# OpenAI配置
OPENAI_API_KEY=sk-your-api-key-here
OPENAI_MODEL=gpt-4o

# MongoDB配置
MONGO_URI=mongodb://localhost:27017
MONGO_DB_NAME=chat_db

# 应用配置
DEBUG=false
LOG_LEVEL=INFO
API_PORT=8000
```

## 🧪 测试

运行测试：

```bash
# 安装测试依赖
pip install pytest pytest-asyncio httpx

# 运行测试
pytest

# 运行测试并查看覆盖率
pytest --cov=app
```

## 📊 监控和日志

- 日志文件位于 `logs/` 目录
- 健康检查端点：`/api/v1/health`
- 详细健康检查：`/api/v1/health/detailed`

## 🔒 安全特性

- CORS配置
- 请求验证
- 错误信息脱敏
- 安全响应头
- 输入长度限制

## 🚀 部署建议

### 生产环境

1. 设置 `DEBUG=false`
2. 使用强密码和密钥
3. 配置反向代理（Nginx）
4. 启用HTTPS
5. 设置日志轮转
6. 配置监控告警

### 性能优化

- 使用连接池
- 启用缓存
- 配置负载均衡
- 优化数据库索引

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
