// MongoDB初始化脚本
db = db.getSiblingDB('chat_db');

// 创建用户
db.createUser({
  user: 'chat_user',
  pwd: 'chat_password',
  roles: [
    {
      role: 'readWrite',
      db: 'chat_db'
    }
  ]
});

// 创建集合
db.createCollection('chat_histories');

// 创建索引
db.chat_histories.createIndex({ "user_id": 1, "session_id": 1, "timestamp": -1 });
db.chat_histories.createIndex({ "timestamp": -1 });

print('MongoDB初始化完成');
