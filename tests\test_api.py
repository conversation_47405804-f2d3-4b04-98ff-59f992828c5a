"""
API测试
"""
import pytest
from httpx import AsyncClient
from fastapi.testclient import TestClient

from app.main import app


@pytest.fixture
def client():
    """测试客户端"""
    return TestClient(app)


@pytest.mark.asyncio
async def test_health_check():
    """测试健康检查"""
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        response = await ac.get("/api/v1/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ok"


@pytest.mark.asyncio
async def test_root_endpoint():
    """测试根路径"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data


def test_chat_send_validation(client):
    """测试聊天发送参数验证"""
    # 测试缺少必要参数
    response = client.post("/api/v1/chat/send", json={})
    assert response.status_code == 422
    
    # 测试无效的消息类型
    response = client.post("/api/v1/chat/send", json={
        "user_id": "test_user",
        "msg_type": "invalid_type",
        "message": "test message"
    })
    assert response.status_code == 422


def test_chat_history_validation(client):
    """测试聊天历史参数验证"""
    # 测试无效的limit参数
    response = client.get("/api/v1/chat/history?user_id=test&session_id=test&limit=0")
    assert response.status_code == 400
    
    response = client.get("/api/v1/chat/history?user_id=test&session_id=test&limit=101")
    assert response.status_code == 400


def test_sessions_endpoint(client):
    """测试会话列表端点"""
    response = client.get("/api/v1/chat/sessions?user_id=test_user")
    assert response.status_code in [200, 500]  # 可能因为数据库连接失败
