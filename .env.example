# 应用配置
APP_NAME=智能客服系统
APP_VERSION=1.0.0
DEBUG=true

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1

# MongoDB配置
MONGO_URI=mongodb://localhost:27017
MONGO_DB_NAME=chat_db
MONGO_COLLECTION_NAME=chat_histories
MONGO_MAX_POOL_SIZE=100
MONGO_MIN_POOL_SIZE=10

# OpenAI配置
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4o
OPENAI_MAX_TOKENS=2048
OPENAI_TIMEOUT=30

# 聊天配置
MAX_HISTORY_LIMIT=50
DEFAULT_HISTORY_LIMIT=20
MAX_MESSAGE_LENGTH=4000

# 文件处理配置
MAX_FILE_SIZE=10485760
FILE_DOWNLOAD_TIMEOUT=10

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALLOWED_ORIGINS=["*"]

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
