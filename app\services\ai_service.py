"""
AI服务模块
"""
import logging
import base64
import requests
from typing import List, Dict, Any
import openai
from openai import OpenAI

from ..config import settings
from ..models import GPTMessage, MessageType, Role

logger = logging.getLogger(__name__)


class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
    
    async def get_ai_response(self, messages: List[Dict[str, Any]]) -> str:
        """获取AI回复"""
        try:
            response = self.client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                max_tokens=settings.OPENAI_MAX_TOKENS,
                timeout=settings.OPENAI_TIMEOUT
            )
            return response.choices[0].message.content
            
        except openai.APIError as e:
            logger.error(f"OpenAI API错误: {e}")
            return f"AI服务暂时不可用，请稍后重试。错误信息: {str(e)}"
        except openai.RateLimitError as e:
            logger.error(f"OpenAI API限流: {e}")
            return "当前请求过多，请稍后重试。"
        except openai.APIConnectionError as e:
            logger.error(f"OpenAI连接错误: {e}")
            return "AI服务连接失败，请检查网络连接。"
        except Exception as e:
            logger.error(f"AI服务未知错误: {e}")
            return f"AI服务出现未知错误: {str(e)}"
    
    def prepare_chat_history(self, chat_messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """准备聊天历史格式"""
        formatted_messages = []
        
        for msg in chat_messages:
            role = msg.get("role")
            msg_type = msg.get("msg_type", MessageType.TEXT)
            content = msg.get("content", "")
            file_url = msg.get("file_url")
            
            if msg_type == MessageType.TEXT:
                formatted_messages.append({
                    "role": role,
                    "content": content
                })
            elif msg_type == MessageType.IMAGE and file_url:
                try:
                    img_base64 = self.url_to_base64(file_url)
                    formatted_messages.append({
                        "role": role,
                        "content": [
                            {"type": "text", "text": "图片如下"},
                            {
                                "type": "image_url", 
                                "image_url": {"url": f"data:image/jpeg;base64,{img_base64}"}
                            }
                        ]
                    })
                except Exception as e:
                    logger.error(f"处理图片失败: {e}")
                    formatted_messages.append({
                        "role": role,
                        "content": f"[图片处理失败: {str(e)}]"
                    })
            elif msg_type == MessageType.FILE and file_url:
                formatted_messages.append({
                    "role": role,
                    "content": f"用户上传了一个文件: {file_url}"
                })
        
        return formatted_messages
    
    def url_to_base64(self, url: str) -> str:
        """将URL转换为base64"""
        try:
            response = requests.get(
                url, 
                timeout=settings.FILE_DOWNLOAD_TIMEOUT,
                headers={'User-Agent': 'Mozilla/5.0 (compatible; ChatBot/1.0)'}
            )
            response.raise_for_status()
            return base64.b64encode(response.content).decode()
        except requests.RequestException as e:
            logger.error(f"下载文件失败: {url}, 错误: {e}")
            raise
    
    def format_new_message(self, msg_type: MessageType, message: str = None, file_url: str = None) -> Dict[str, Any]:
        """格式化新消息"""
        if msg_type == MessageType.TEXT:
            return {"role": "user", "content": message}
        elif msg_type == MessageType.IMAGE:
            img_base64 = self.url_to_base64(file_url)
            return {
                "role": "user",
                "content": [
                    {"type": "text", "text": "请识别这张图片"},
                    {
                        "type": "image_url", 
                        "image_url": {"url": f"data:image/jpeg;base64,{img_base64}"}
                    }
                ]
            }
        elif msg_type == MessageType.FILE:
            return {
                "role": "user",
                "content": f"请分析这个文件: {file_url}"
            }


# 全局AI服务实例
ai_service = AIService()
