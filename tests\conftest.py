"""
测试配置
"""
import pytest
import os
from unittest.mock import Mock, patch

# 设置测试环境变量
os.environ["OPENAI_API_KEY"] = "sk-test-key-for-testing"
os.environ["MONGO_URI"] = "mongodb://localhost:27017"
os.environ["DEBUG"] = "true"


@pytest.fixture
def mock_db_manager():
    """模拟数据库管理器"""
    with patch("app.database.db_manager") as mock:
        mock.is_connected = True
        mock.connect.return_value = None
        mock.disconnect.return_value = None
        mock.insert_message.return_value = "test_id"
        mock.get_chat_history.return_value = []
        mock.get_user_sessions.return_value = []
        yield mock


@pytest.fixture
def mock_ai_service():
    """模拟AI服务"""
    with patch("app.services.ai_service.ai_service") as mock:
        mock.get_ai_response.return_value = "测试AI回复"
        mock.format_new_message.return_value = {"role": "user", "content": "test"}
        mock.prepare_chat_history.return_value = []
        yield mock
