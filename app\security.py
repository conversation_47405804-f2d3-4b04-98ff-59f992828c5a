"""
安全模块
"""
import time
import hashlib
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import HTTPException, Request, Depends
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
import jwt

from .config import settings

logger = logging.getLogger(__name__)

# 简单的内存限流器
class RateLimiter:
    """简单的内存限流器"""
    
    def __init__(self):
        self.requests: Dict[str, list] = {}
    
    def is_allowed(self, key: str, max_requests: int = 60, window_seconds: int = 60) -> bool:
        """检查是否允许请求"""
        now = time.time()
        
        # 清理过期记录
        if key in self.requests:
            self.requests[key] = [
                req_time for req_time in self.requests[key] 
                if now - req_time < window_seconds
            ]
        else:
            self.requests[key] = []
        
        # 检查是否超过限制
        if len(self.requests[key]) >= max_requests:
            return False
        
        # 记录当前请求
        self.requests[key].append(now)
        return True


# 全局限流器实例
rate_limiter = RateLimiter()


class SecurityManager:
    """安全管理器"""
    
    def __init__(self):
        self.security = HTTPBearer(auto_error=False)
    
    def create_access_token(self, data: Dict[str, Any]) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire})
        
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm="HS256")
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("令牌已过期")
            return None
        except jwt.JWTError as e:
            logger.warning(f"令牌验证失败: {e}")
            return None
    
    def get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """验证密码"""
        return self.hash_password(password) == hashed


# 全局安全管理器实例
security_manager = SecurityManager()


async def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security_manager.security)
) -> Optional[Dict[str, Any]]:
    """获取当前用户（可选认证）"""
    if not credentials:
        return None
    
    payload = security_manager.verify_token(credentials.credentials)
    if not payload:
        return None
    
    return payload


async def require_auth(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security_manager.security)
) -> Dict[str, Any]:
    """要求认证"""
    if not credentials:
        raise HTTPException(status_code=401, detail="需要认证")
    
    payload = security_manager.verify_token(credentials.credentials)
    if not payload:
        raise HTTPException(status_code=401, detail="无效的令牌")
    
    return payload


async def rate_limit_check(request: Request):
    """限流检查"""
    client_ip = security_manager.get_client_ip(request)
    
    if not rate_limiter.is_allowed(client_ip, max_requests=100, window_seconds=60):
        logger.warning(f"IP {client_ip} 触发限流")
        raise HTTPException(status_code=429, detail="请求过于频繁，请稍后重试")


def validate_input_length(text: str, max_length: int = None) -> str:
    """验证输入长度"""
    if max_length is None:
        max_length = settings.MAX_MESSAGE_LENGTH
    
    if len(text) > max_length:
        raise HTTPException(
            status_code=400, 
            detail=f"输入内容过长，最大长度为 {max_length} 字符"
        )
    
    return text


def sanitize_input(text: str) -> str:
    """清理输入内容"""
    # 移除潜在的恶意字符
    dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
    for char in dangerous_chars:
        text = text.replace(char, '')
    
    return text.strip()
