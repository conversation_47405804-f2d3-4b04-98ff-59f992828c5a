version: '3.8'

services:
  # 智能客服API服务
  chat-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - MONGO_URI=mongodb://mongodb:27017
      - DEBUG=false
      - LOG_LEVEL=INFO
    env_file:
      - .env
    depends_on:
      - mongodb
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - chat-network

  # MongoDB数据库
  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=chat_db
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    restart: unless-stopped
    networks:
      - chat-network

  # MongoDB管理界面（可选）
  mongo-express:
    image: mongo-express:1.0
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=admin
      - ME_CONFIG_MONGODB_ADMINPASSWORD=password
      - ME_CONFIG_MONGODB_URL=**************************************/
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin
    depends_on:
      - mongodb
    restart: unless-stopped
    networks:
      - chat-network

volumes:
  mongodb_data:

networks:
  chat-network:
    driver: bridge
