"""
配置管理模块
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import validator


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用配置
    APP_NAME: str = "智能客服系统"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # API配置
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_PREFIX: str = "/api/v1"
    
    # MongoDB配置
    MONGO_URI: str = "mongodb://localhost:27017"
    MONGO_DB_NAME: str = "chat_db"
    MONGO_COLLECTION_NAME: str = "chat_histories"
    MONGO_MAX_POOL_SIZE: int = 100
    MONGO_MIN_POOL_SIZE: int = 10
    
    # OpenAI配置
    OPENAI_API_KEY: str = ""
    OPENAI_MODEL: str = "gpt-4o"
    OPENAI_MAX_TOKENS: int = 2048
    OPENAI_TIMEOUT: int = 30
    
    # 聊天配置
    MAX_HISTORY_LIMIT: int = 50
    DEFAULT_HISTORY_LIMIT: int = 20
    MAX_MESSAGE_LENGTH: int = 4000
    
    # 文件处理配置
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_IMAGE_TYPES: list = ["image/jpeg", "image/png", "image/gif", "image/webp"]
    FILE_DOWNLOAD_TIMEOUT: int = 10
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALLOWED_ORIGINS: list = ["*"]
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    @validator("OPENAI_API_KEY")
    def validate_openai_key(cls, v):
        if not v or v == "sk-xxxxxxxxxxxxxxxx" or v.startswith("sk-test"):
            if os.getenv("TESTING") != "true":
                raise ValueError("请设置有效的 OPENAI_API_KEY")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 全局配置实例
settings = Settings()
