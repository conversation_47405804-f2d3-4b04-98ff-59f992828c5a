"""
数据库操作模块
"""
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.errors import ConnectionFailure, OperationFailure
from langchain.memory import MongoDBChatMessageHistory

from .config import settings
from .models import ChatMessage, Role, MessageType

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self._client: Optional[MongoClient] = None
        self._db = None
        self._collection = None
    
    def connect(self):
        """连接数据库"""
        try:
            self._client = MongoClient(
                settings.MONGO_URI,
                maxPoolSize=settings.MONGO_MAX_POOL_SIZE,
                minPoolSize=settings.MONGO_MIN_POOL_SIZE,
                serverSelectionTimeoutMS=5000
            )
            # 测试连接
            self._client.admin.command('ping')
            
            self._db = self._client[settings.MONGO_DB_NAME]
            self._collection = self._db[settings.MONGO_COLLECTION_NAME]
            
            # 创建索引
            self._create_indexes()
            
            logger.info("数据库连接成功")
            
        except ConnectionFailure as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def disconnect(self):
        """断开数据库连接"""
        if self._client:
            self._client.close()
            logger.info("数据库连接已关闭")
    
    def _create_indexes(self):
        """创建数据库索引"""
        try:
            # 用户ID和会话ID的复合索引
            self._collection.create_index([
                ("user_id", ASCENDING),
                ("session_id", ASCENDING),
                ("timestamp", DESCENDING)
            ], name="user_session_time_idx")

            # 时间戳索引
            self._collection.create_index([("timestamp", DESCENDING)], name="timestamp_idx")

            # 用户ID索引（用于获取用户所有会话）
            self._collection.create_index([("user_id", ASCENDING)], name="user_id_idx")

            # 角色和消息类型索引（用于统计分析）
            self._collection.create_index([
                ("role", ASCENDING),
                ("msg_type", ASCENDING)
            ], name="role_msgtype_idx")

            logger.info("数据库索引创建完成")

        except OperationFailure as e:
            logger.error(f"创建索引失败: {e}")
    
    def insert_message(self, message_doc: Dict[str, Any]) -> str:
        """插入消息"""
        try:
            result = self._collection.insert_one(message_doc)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"插入消息失败: {e}")
            raise
    
    def get_chat_history(
        self, 
        user_id: str, 
        session_id: str, 
        limit: int = 20
    ) -> List[ChatMessage]:
        """获取聊天历史"""
        try:
            cursor = self._collection.find({
                "user_id": user_id,
                "session_id": session_id
            }).sort("timestamp", DESCENDING).limit(limit)
            
            messages = []
            for doc in reversed(list(cursor)):
                message = ChatMessage(
                    role=Role(doc.get("role")),
                    msg_type=MessageType(doc.get("msg_type")),
                    content=doc.get("content", ""),
                    file_url=doc.get("file_url"),
                    timestamp=doc.get("timestamp")
                )
                messages.append(message)
            
            return messages
            
        except Exception as e:
            logger.error(f"获取聊天历史失败: {e}")
            raise
    
    def get_user_sessions(self, user_id: str) -> List[str]:
        """获取用户的所有会话"""
        try:
            sessions = self._collection.distinct("session_id", {"user_id": user_id})
            return sessions
        except Exception as e:
            logger.error(f"获取用户会话失败: {e}")
            raise
    
    def get_langchain_history(self, user_id: str, session_id: str) -> MongoDBChatMessageHistory:
        """获取LangChain历史对象"""
        return MongoDBChatMessageHistory(
            connection_string=settings.MONGO_URI,
            session_id=f"{user_id}-{session_id}",
            database_name=settings.MONGO_DB_NAME,
            collection_name=settings.MONGO_COLLECTION_NAME
        )
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            stats = self._db.command("dbStats")
            collection_stats = self._db.command("collStats", settings.MONGO_COLLECTION_NAME)

            return {
                "database_size": stats.get("dataSize", 0),
                "collection_count": collection_stats.get("count", 0),
                "index_count": len(collection_stats.get("indexSizes", {})),
                "average_object_size": collection_stats.get("avgObjSize", 0)
            }
        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return {}

    def cleanup_old_messages(self, days: int = 30) -> int:
        """清理旧消息"""
        try:
            from datetime import datetime, timedelta
            cutoff_date = datetime.now() - timedelta(days=days)

            result = self._collection.delete_many({
                "timestamp": {"$lt": cutoff_date.isoformat()}
            })

            logger.info(f"清理了 {result.deleted_count} 条旧消息")
            return result.deleted_count

        except Exception as e:
            logger.error(f"清理旧消息失败: {e}")
            return 0

    @property
    def is_connected(self) -> bool:
        """检查数据库连接状态"""
        try:
            if self._client:
                self._client.admin.command('ping')
                return True
        except:
            pass
        return False


# 全局数据库管理器实例
db_manager = DatabaseManager()
