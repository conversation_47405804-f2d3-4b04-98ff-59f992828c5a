"""
聊天服务模块
"""
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List

from ..database import db_manager
from ..models import ChatSendRequest, ChatSendResponse, MessageType, Role
from ..cache import cache_manager
from .ai_service import ai_service

logger = logging.getLogger(__name__)


class ChatService:
    """聊天服务类"""
    
    async def send_message(self, request: ChatSendRequest, echo_fields: Dict[str, Any]) -> ChatSendResponse:
        """处理发送消息"""
        # 生成会话ID
        session_id = request.session_id or str(uuid.uuid4())
        
        # 保存用户消息
        user_msg_id = await self._save_user_message(request, session_id)
        
        # 获取聊天历史
        chat_history = await self._get_formatted_chat_history(
            request.user_id, 
            session_id
        )
        
        # 添加当前消息到历史
        current_message = ai_service.format_new_message(
            request.msg_type, 
            request.message, 
            request.file_url
        )
        chat_history.append(current_message)
        
        # 获取AI回复
        ai_reply = await ai_service.get_ai_response(chat_history)
        
        # 保存AI回复
        await self._save_ai_message(request.user_id, session_id, ai_reply)
        
        # 更新LangChain历史
        await self._update_langchain_history(request, session_id, ai_reply)
        
        return ChatSendResponse(
            reply=ai_reply,
            reply_type=MessageType.TEXT,
            session_id=session_id,
            echo_fields=echo_fields
        )
    
    async def _save_user_message(self, request: ChatSendRequest, session_id: str) -> str:
        """保存用户消息"""
        msg_doc = {
            "user_id": request.user_id,
            "session_id": session_id,
            "role": Role.USER.value,
            "msg_type": request.msg_type.value,
            "content": request.message if request.msg_type == MessageType.TEXT else "",
            "file_url": request.file_url if request.msg_type in [MessageType.IMAGE, MessageType.FILE] else "",
            "timestamp": datetime.now().isoformat()
        }
        
        return db_manager.insert_message(msg_doc)
    
    async def _save_ai_message(self, user_id: str, session_id: str, reply: str) -> str:
        """保存AI回复消息"""
        ai_msg_doc = {
            "user_id": user_id,
            "session_id": session_id,
            "role": Role.ASSISTANT.value,
            "msg_type": MessageType.TEXT.value,
            "content": reply,
            "file_url": "",
            "timestamp": datetime.now().isoformat()
        }
        
        return db_manager.insert_message(ai_msg_doc)
    
    async def _get_formatted_chat_history(self, user_id: str, session_id: str) -> List[Dict[str, Any]]:
        """获取格式化的聊天历史"""
        # 从数据库获取历史消息
        raw_messages = []
        try:
            cursor = db_manager._collection.find({
                "user_id": user_id,
                "session_id": session_id
            }).sort("timestamp", 1).limit(10)  # 限制历史消息数量
            
            raw_messages = list(cursor)
        except Exception as e:
            logger.error(f"获取聊天历史失败: {e}")
        
        # 格式化消息
        return ai_service.prepare_chat_history(raw_messages)
    
    async def _update_langchain_history(self, request: ChatSendRequest, session_id: str, ai_reply: str):
        """更新LangChain历史"""
        try:
            history = db_manager.get_langchain_history(request.user_id, session_id)
            
            # 添加用户消息
            if request.msg_type == MessageType.TEXT:
                history.add_user_message(request.message)
            elif request.msg_type in [MessageType.IMAGE, MessageType.FILE]:
                history.add_user_message(f"[{request.msg_type.value}]({request.file_url})")
            
            # 添加AI回复
            history.add_ai_message(ai_reply)
            
        except Exception as e:
            logger.error(f"更新LangChain历史失败: {e}")
    
    def get_chat_history(self, user_id: str, session_id: str, limit: int = 20):
        """获取聊天历史"""
        return db_manager.get_chat_history(user_id, session_id, limit)
    
    def get_user_sessions(self, user_id: str):
        """获取用户会话列表"""
        return db_manager.get_user_sessions(user_id)


# 全局聊天服务实例
chat_service = ChatService()
