import openai
import base64
import requests

# 填你的 OpenAI API Key
OPENAI_API_KEY = "sk-xxxxxxxxxxxxxxxx"

def url_to_base64(url):
    resp = requests.get(url, timeout=8)
    resp.raise_for_status()
    return base64.b64encode(resp.content).decode()

def call_gpt4o(messages):
    """
    messages: 格式
    [
      {"role": "user", "content": "你好"},
      {"role": "assistant", "content": "你好，有什么可以帮您？"},
      {"role": "user", "content": [{"type": "text", "text": "这是什么？"}, {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,xxxx"}}]}
    ]
    """
    openai.api_key = OPENAI_API_KEY
    response = openai.chat.completions.create(
        model="gpt-4o",
        messages=messages,
        max_tokens=2048
    )
    return response.choices[0].message.content
