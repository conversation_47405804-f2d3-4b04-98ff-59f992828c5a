"""
缓存模块
"""
import json
import time
import logging
from typing import Any, Optional, Dict
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class MemoryCache:
    """内存缓存"""
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._last_cleanup = time.time()
        self._cleanup_interval = 300  # 5分钟清理一次
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        self._cleanup_expired()
        
        if key not in self._cache:
            return None
        
        item = self._cache[key]
        if item['expires_at'] < time.time():
            del self._cache[key]
            return None
        
        return item['value']
    
    def set(self, key: str, value: Any, ttl: int = 300) -> None:
        """设置缓存值"""
        self._cache[key] = {
            'value': value,
            'expires_at': time.time() + ttl,
            'created_at': time.time()
        }
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        if key in self._cache:
            del self._cache[key]
            return True
        return False
    
    def clear(self) -> None:
        """清空缓存"""
        self._cache.clear()
    
    def _cleanup_expired(self) -> None:
        """清理过期缓存"""
        now = time.time()
        if now - self._last_cleanup < self._cleanup_interval:
            return
        
        expired_keys = [
            key for key, item in self._cache.items()
            if item['expires_at'] < now
        ]
        
        for key in expired_keys:
            del self._cache[key]
        
        self._last_cleanup = now
        
        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期缓存项")
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        self._cleanup_expired()
        return {
            'total_items': len(self._cache),
            'memory_usage_estimate': sum(
                len(str(item['value'])) for item in self._cache.values()
            ),
            'oldest_item': min(
                (item['created_at'] for item in self._cache.values()),
                default=None
            )
        }


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.memory_cache = MemoryCache()
    
    def get_user_sessions_cache_key(self, user_id: str) -> str:
        """获取用户会话缓存键"""
        return f"user_sessions:{user_id}"
    
    def get_chat_history_cache_key(self, user_id: str, session_id: str, limit: int) -> str:
        """获取聊天历史缓存键"""
        return f"chat_history:{user_id}:{session_id}:{limit}"
    
    def get_ai_response_cache_key(self, messages_hash: str) -> str:
        """获取AI响应缓存键"""
        return f"ai_response:{messages_hash}"
    
    def hash_messages(self, messages: list) -> str:
        """计算消息列表的哈希值"""
        import hashlib
        messages_str = json.dumps(messages, sort_keys=True)
        return hashlib.md5(messages_str.encode()).hexdigest()
    
    def cache_user_sessions(self, user_id: str, sessions: list, ttl: int = 300) -> None:
        """缓存用户会话"""
        key = self.get_user_sessions_cache_key(user_id)
        self.memory_cache.set(key, sessions, ttl)
    
    def get_cached_user_sessions(self, user_id: str) -> Optional[list]:
        """获取缓存的用户会话"""
        key = self.get_user_sessions_cache_key(user_id)
        return self.memory_cache.get(key)
    
    def cache_chat_history(self, user_id: str, session_id: str, limit: int, history: list, ttl: int = 180) -> None:
        """缓存聊天历史"""
        key = self.get_chat_history_cache_key(user_id, session_id, limit)
        self.memory_cache.set(key, history, ttl)
    
    def get_cached_chat_history(self, user_id: str, session_id: str, limit: int) -> Optional[list]:
        """获取缓存的聊天历史"""
        key = self.get_chat_history_cache_key(user_id, session_id, limit)
        return self.memory_cache.get(key)
    
    def invalidate_user_cache(self, user_id: str) -> None:
        """使用户相关缓存失效"""
        # 删除用户会话缓存
        sessions_key = self.get_user_sessions_cache_key(user_id)
        self.memory_cache.delete(sessions_key)
        
        # 删除用户的聊天历史缓存（这里简化处理，实际可能需要更精确的清理）
        # 在实际应用中，可以维护一个用户到缓存键的映射
    
    def cache_ai_response(self, messages: list, response: str, ttl: int = 3600) -> None:
        """缓存AI响应（用于相同问题的快速响应）"""
        messages_hash = self.hash_messages(messages)
        key = self.get_ai_response_cache_key(messages_hash)
        self.memory_cache.set(key, response, ttl)
    
    def get_cached_ai_response(self, messages: list) -> Optional[str]:
        """获取缓存的AI响应"""
        messages_hash = self.hash_messages(messages)
        key = self.get_ai_response_cache_key(messages_hash)
        return self.memory_cache.get(key)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return self.memory_cache.stats()


# 全局缓存管理器实例
cache_manager = CacheManager()
